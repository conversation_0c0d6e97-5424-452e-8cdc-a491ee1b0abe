csdn:
  api:
    categories: Java场景面试宝典
    cookie: fid=20_42147705314-1734618269829-978382; Hm_lvt_ec8a58cd84a81850bcbd95ef89524721=1734618270; __gads=ID=c6f3204e37437d88:T=1734618273:RT=1734618273:S=ALNI_MbKocsrHYWfzirtFhd3C4HDHdlUSw; __gpi=UID=00000fad02f16afe:T=1734618273:RT=1734618273:S=ALNI_MaS7l4K9JZLyKw-PFdH9dJjAthgoA; FCNEC=%5B%5B%22AKsRol_JLf8AbpyuToB3ibf9AnpnZDhLNJagllF7rzqSgimSN0Ln-8IEV6KdlqqCqTBxUE7J4Iqqi16UtWAOoUltBPsJWE6pu9VsAQOd2kD-MfYtPnqXPldaon-A_zanb2LgAZd7LvF0rm7pJd6-jWIIUJpjTCo9Xg%3D%3D%22%5D%5D; c_dl_fref=https://www.google.com/; c_dl_fpage=/download/weixin_49090381/15707333; c_dl_um=-; c_dl_prid=1734622701658_203305; c_dl_rid=1734622705028_563723; UN=belongtocode; p_uid=U010000; uuid_tt_dd=10_18512144130-1747663447155-546225; UserName=belongtocode; UserInfo=d31cc9dd97774a6d9edcb9f6c350aa7a; UserToken=d31cc9dd97774a6d9edcb9f6c350aa7a; UserNick=Apple_Web; AU=8EF; BT=*************; csdn_newcert_belongtocode=1; c_adb=1; _gid=GA1.2.*********.**********; yd_captcha_token=MTc1NTc5NDM0NzY3MV8xMDYuMTIyLjE4Ni4yNV85ZmVmYTY2NTI2ZDk1N2UwMWNjZTkxNWNkZGZlNjdmZWU2NQ%3D%3D; creative_btn_mp=3; https_waf_cookie=a98fd34d-116a-4df249e72725c3a728fd73a87c66f1d0fc99; c_segment=1; dc_sid=58fa4d6008906f42ac136ed4886854ff; Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac=**********,**********,**********,**********; HMACCOUNT=F0B7EDD9FBFC55CD; _clck=okf7mx%5E2%5Efyo%5E0%5E1814; is_advert=1; c_first_ref=www.google.com; waf_captcha_marker=a97073321397830be02947c773ed681794efb9354b9bef8d1c5a308bdb50a244; c_first_page=https%3A//blog.csdn.net/s674334235/article/details/*********; Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac=**********; _ga_7W1N0GEY1P=GS2.1.s1755846028$o13$g1$t1755846042$j46$l0$h0; _ga=GA1.1.**********.**********; _clsk=dfdkpk%5E1755853368109%5E1%5E0%5Ej.clarity.ms%2Fcollect; dc_session_id=10_1755853366023.289572; dc_tos=t1e14s; c_pref=https%3A//www.google.com/; c_ref=https%3A//www.csdn.net/; c_dsid=11_1755853373566.971705; c_page_id=default; log_Id_view=1

spring:
  application:
    name: mcp-server-csdn

  ai:
    mcp:
      server:
        name: ${spring.application.name}
        version: 1.0.0

  main:
    banner-mode: off
# stdio 模式打开，sse 模式，注释掉。
#    web-application-type: none

logging:
# stdio 模式打开，sse 模式，注释掉。
#  pattern:
#    console:
  file:
    name: data/log/${spring.application.name}.log

server:
  port: 8101
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true
